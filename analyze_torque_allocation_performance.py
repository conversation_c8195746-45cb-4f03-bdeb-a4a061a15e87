#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析扭矩分配逻辑的性能瓶颈
"""

import numpy as np
import time
from dpm import Input


def create_test_input():
    """创建测试输入数据"""
    inp = Input()
    inp.X = {1: 0.5}  # SOC = 50%
    inp.U = {1: 0.5, 2: 1}  # 扭矩分配比例和工作模式
    inp.W = {1: 15.0, 2: 0.5}  # 15 m/s, 0.5 m/s²
    inp.Ts = 1.0
    return inp


def benchmark_torque_allocation_components():
    """分析扭矩分配各个组件的性能"""
    print("扭矩分配逻辑性能分析")
    print("=" * 60)
    
    inp = create_test_input()
    
    # 预计算一些基础变量 (模拟函数前面的计算)
    wv = inp.W[1] / 0.324
    dwv = inp.W[2] / 0.324
    Tv = (100.95 + 0.7667 * (inp.W[1] * 3.6) +
          0.0271 * (inp.W[1] * 3.6)**2 + 1820 * inp.W[2]) * 0.324
    
    ateff = 0.94
    p3eff = 0.96
    r_gear = np.array([2.75, 2.75, 2.75, 2.75])
    p1_gear = 1.0
    p3_gear = 10.03
    
    Pchrg = (1 - inp.U[1]) * 10000
    Pchrg = Pchrg * (Pchrg > 10000)
    
    # 模拟曲轴转速计算
    _oolpwr = 1000 * np.array([0.00, 7.54, 10.47, 12.57, 16.13, 16.76, 18.85, 20.94,
                              23.04, 27.65, 29.95, 32.25, 34.56, 33.51, 35.61, 37.70, 40])
    _oolspd = np.pi/30 * np.array([0, 800, 1000, 1200, 1400, 1600, 1800, 2000,
                                  2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800])
    _ooltrq = np.array([0, 90, 100, 100, 110, 100, 100, 100, 100, 110, 110, 110, 110, 100, 100, 100, 100])
    
    wg = ((inp.U[1] != 1) * (inp.U[2] == 0) *
          np.interp(Pchrg, _oolpwr, _oolspd) +
          (inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
          r_gear[0] * wv)
    
    wm1 = (inp.U[1] != 1) * (p1_gear * wg)
    wm3 = p3_gear * wv
    
    dwg = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
           r_gear[0] * dwv)
    dwm1 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
            p1_gear * dwg)
    dwm3 = p3_gear * dwv
    
    # 现在分析扭矩分配各个部分
    num_runs = 10000
    
    print(f"测试 {num_runs} 次运行的各个组件耗时:")
    print("-" * 60)
    
    # 1. 拖拽扭矩计算
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Te0 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
               dwg * 0.10)
        Tm10 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
                dwm1 * 0.03)
        Tm30 = dwm3 * 0.03
    drag_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"拖拽扭矩计算: {drag_time:.4f} ms")
    
    # 2. 车轮总需求扭矩计算
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Ttotwhl = (((inp.U[1] == 1) + (inp.U[1] != 1) * (inp.U[2] == 0)) *
                   (Tm30 * p3_gear + Tv) +
                   (inp.U[1] != 1) * (inp.U[2] == 2) *
                   ((Te0 + Tm10 * p1_gear) * r_gear[0] + Tm30 * p3_gear + Tv))
    wheel_torque_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"车轮总需求扭矩: {wheel_torque_time:.4f} ms")
    
    # 3. 离合器扭矩计算 (最复杂的部分)
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Ttotclu = ((inp.U[1] != 1) * (inp.U[2] == 1) *
                   ((Tm30 * p3_gear + Tv) / r_gear[0] *
                    (((Tm30 * p3_gear + Tv) >= 0) / ateff +
                     ((Tm30 * p3_gear + Tv) < 0) * ateff) + Te0 + Tm10 * p1_gear))
    clutch_torque_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"离合器扭矩计算: {clutch_torque_time:.4f} ms")
    
    # 4. 发动机扭矩计算
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Te = ((inp.U[1] != 1) *
              ((inp.U[2] == 0) * np.interp(Pchrg, _oolpwr, _ooltrq) +
               (inp.U[2] == 2) * ((Ttotwhl > 0) * (1 - inp.U[1]) *
                                    Ttotwhl / r_gear[0] / ateff + (Ttotwhl <= 0) * 0.1) +
               (inp.U[2] == 1) * ((Ttotclu > 0) * (1 - inp.U[1]) * Ttotclu +
                                    (Ttotclu <= 0) * 0.1)))
    engine_torque_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"发动机扭矩计算: {engine_torque_time:.4f} ms")
    
    # 5. P1电机扭矩计算
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Tm1 = ((inp.U[1] != 1) * (inp.U[2] == 0) * (-1/p1_gear * Te) +
               (inp.U[1] != 1) * (inp.U[2] == 1) *
               (((Ttotclu > 0) + (wv > 9) * (Ttotclu <= 0)) * inp.U[1] * Ttotclu / p1_gear))
    motor1_torque_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"P1电机扭矩计算: {motor1_torque_time:.4f} ms")
    
    # 6. P3电机扭矩计算 (最复杂的部分)
    start_time = time.perf_counter()
    for _ in range(num_runs):
        Tm3 = (((inp.U[1] == 1) + (inp.U[1] != 1) * (inp.U[2] == 0)) *
               ((wm3 > 0) * ((Ttotwhl > 0) / p3eff + (wv > 9) * (Ttotwhl <= 0) * p3eff) *
                Ttotwhl / p3_gear) +
               (inp.U[1] != 1) * (inp.U[2] == 2) *
               ((wm3 > 0) * (Ttotwhl > 0) * Ttotwhl *
                ((inp.U[1] >= 0) * inp.U[1] / p3eff +
                 (inp.U[1] < 0) * inp.U[1] * p3eff) / p3_gear))
    motor3_torque_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"P3电机扭矩计算: {motor3_torque_time:.4f} ms")
    
    # 7. 不可行检查
    start_time = time.perf_counter()
    for _ in range(num_runs):
        inps = ((Te > 0) * (wg < 100) + (Ttotwhl < 0) * (Tm3 > 0) +
                (Ttotclu < 0) * (Tm1 > 0))
    infeasible_time = (time.perf_counter() - start_time) / num_runs * 1000
    print(f"不可行检查: {infeasible_time:.4f} ms")
    
    # 总计
    total_component_time = (drag_time + wheel_torque_time + clutch_torque_time + 
                           engine_torque_time + motor1_torque_time + motor3_torque_time + 
                           infeasible_time)
    
    print("-" * 60)
    print(f"组件总计: {total_component_time:.4f} ms")
    
    return {
        '拖拽扭矩计算': drag_time,
        '车轮总需求扭矩': wheel_torque_time,
        '离合器扭矩计算': clutch_torque_time,
        '发动机扭矩计算': engine_torque_time,
        'P1电机扭矩计算': motor1_torque_time,
        'P3电机扭矩计算': motor3_torque_time,
        '不可行检查': infeasible_time,
        '总计': total_component_time
    }


def analyze_conditional_operations():
    """分析条件运算的性能影响"""
    print("\n条件运算性能分析")
    print("=" * 60)
    
    inp = create_test_input()
    num_runs = 100000
    
    # 测试简单条件运算
    print(f"测试 {num_runs} 次条件运算:")
    print("-" * 40)
    
    # 1. 简单比较
    start_time = time.perf_counter()
    for _ in range(num_runs):
        result = (inp.U[1] != 1)
    simple_compare_time = (time.perf_counter() - start_time) / num_runs * 1000000  # 微秒
    print(f"简单比较 (inp.U[1] != 1): {simple_compare_time:.3f} μs")
    
    # 2. 复合条件
    start_time = time.perf_counter()
    for _ in range(num_runs):
        result = ((inp.U[2] == 1) + (inp.U[2] == 2))
    compound_condition_time = (time.perf_counter() - start_time) / num_runs * 1000000
    print(f"复合条件 ((U2==1)+(U2==2)): {compound_condition_time:.3f} μs")
    
    # 3. 嵌套条件
    start_time = time.perf_counter()
    for _ in range(num_runs):
        result = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)))
    nested_condition_time = (time.perf_counter() - start_time) / num_runs * 1000000
    print(f"嵌套条件: {nested_condition_time:.3f} μs")
    
    # 4. 复杂条件表达式
    Ttotwhl = 100.0  # 示例值
    ateff = 0.94
    start_time = time.perf_counter()
    for _ in range(num_runs):
        result = (((Ttotwhl >= 0) / ateff + (Ttotwhl < 0) * ateff))
    complex_condition_time = (time.perf_counter() - start_time) / num_runs * 1000000
    print(f"复杂条件表达式: {complex_condition_time:.3f} μs")


def analyze_optimization_opportunities():
    """分析优化机会"""
    print("\n优化机会分析")
    print("=" * 60)
    
    print("1. 条件判断优化:")
    print("   - 当前使用大量嵌套的条件运算")
    print("   - 每个条件都会触发数组比较和布尔运算")
    print("   - 可以预计算条件标志减少重复计算")
    
    print("\n2. 数学运算优化:")
    print("   - 存在重复的中间计算 (如 Tm30 * p3_gear)")
    print("   - 可以提取公共子表达式")
    print("   - 减少除法运算，预计算倒数")
    
    print("\n3. 分支优化:")
    print("   - 根据工作模式 (inp.U[2]) 进行分支处理")
    print("   - 避免计算不需要的分支")
    print("   - 使用查找表或预计算结果")
    
    print("\n4. 内存访问优化:")
    print("   - 减少重复的字典访问 (inp.U[1], inp.U[2])")
    print("   - 将常用值缓存到局部变量")


def suggest_optimizations():
    """提出具体的优化建议"""
    print("\n具体优化建议")
    print("=" * 60)
    
    print("🚀 立即可实施的优化 (预期提升 30-50%):")
    print("-" * 50)
    
    print("1. 预计算条件标志:")
    print("   ```python")
    print("   # 当前写法")
    print("   Te0 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) * dwg * 0.10)")
    print("   ")
    print("   # 优化写法")
    print("   is_not_pure_electric = (inp.U[1] != 1)")
    print("   is_parallel_or_series = ((inp.U[2] == 1) + (inp.U[2] == 2))")
    print("   Te0 = is_not_pure_electric * is_parallel_or_series * dwg * 0.10")
    print("   ```")
    
    print("\n2. 提取公共子表达式:")
    print("   ```python")
    print("   # 当前写法")
    print("   # Tm30 * p3_gear 在多处重复计算")
    print("   ")
    print("   # 优化写法")
    print("   Tm30_gear = Tm30 * p3_gear")
    print("   Ttotwhl = (condition1 * (Tm30_gear + Tv) + ...)")
    print("   ```")
    
    print("\n3. 缓存字典访问:")
    print("   ```python")
    print("   # 当前写法")
    print("   # inp.U[1] 和 inp.U[2] 被多次访问")
    print("   ")
    print("   # 优化写法")
    print("   U1, U2 = inp.U[1], inp.U[2]")
    print("   # 后续使用 U1, U2 替代 inp.U[1], inp.U[2]")
    print("   ```")
    
    print("\n4. 工作模式分支优化:")
    print("   ```python")
    print("   # 当前写法")
    print("   # 所有模式的计算都执行，然后用条件选择")
    print("   ")
    print("   # 优化写法")
    print("   if U2 == 0:  # 串联模式")
    print("       # 只计算串联模式相关的扭矩")
    print("   elif U2 == 1:  # 并联模式")
    print("       # 只计算并联模式相关的扭矩")
    print("   elif U2 == 2:  # 混合模式")
    print("       # 只计算混合模式相关的扭矩")
    print("   ```")
    
    print("\n🔧 中期优化 (预期提升 50-70%):")
    print("-" * 50)
    print("5. 使用NumPy的where函数替代条件乘法")
    print("6. 预计算常用的效率倒数 (1/ateff, 1/p3eff)")
    print("7. 使用查找表存储常见工作点的扭矩分配结果")
    
    print("\n⚡ 高级优化 (预期提升 70%+):")
    print("-" * 50)
    print("8. 使用Numba JIT编译扭矩分配函数")
    print("9. 重构为状态机模式，避免重复的条件判断")
    print("10. 使用SIMD指令优化向量运算")


def main():
    """主函数"""
    # 分析各组件性能
    component_times = benchmark_torque_allocation_components()
    
    # 分析条件运算
    analyze_conditional_operations()
    
    # 分析优化机会
    analyze_optimization_opportunities()
    
    # 提出优化建议
    suggest_optimizations()
    
    print("\n" + "=" * 80)
    print("📊 扭矩分配性能分析总结:")
    print("=" * 80)
    
    # 找出最耗时的组件
    max_component = max(component_times.items(), key=lambda x: x[1] if x[0] != '总计' else 0)
    print(f"🔍 最耗时组件: {max_component[0]} ({max_component[1]:.4f} ms)")
    
    # 计算优化潜力
    total_time = component_times['总计']
    estimated_optimized_time = total_time * 0.4  # 预期优化后保留40%时间
    speedup = total_time / estimated_optimized_time
    
    print(f"📈 当前总耗时: {total_time:.4f} ms")
    print(f"🎯 预期优化后: {estimated_optimized_time:.4f} ms")
    print(f"🚀 预期加速比: {speedup:.1f}x")
    print("=" * 80)


if __name__ == "__main__":
    main()
