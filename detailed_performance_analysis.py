#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细性能分析 - 分析 phev_ht21_serip1p3.py 函数内部各部分的性能
"""

import numpy as np
import time
from scipy.interpolate import interp1d, RegularGridInterpolator
from dpm import Input


def create_test_input():
    """创建测试输入数据"""
    inp = Input()
    inp.X = {1: 0.5}  # SOC = 50%
    inp.U = {1: 0.5, 2: 1}  # 扭矩分配比例和工作模式
    inp.W = {1: 15.0, 2: 0.5}  # 15 m/s, 0.5 m/s²
    inp.Ts = 1.0
    return inp


def benchmark_section(section_name, func, *args, num_runs=1000):
    """测试代码段的性能"""
    # 预热
    for _ in range(10):
        func(*args)
    
    start_time = time.perf_counter()
    for _ in range(num_runs):
        func(*args)
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    avg_time = total_time / num_runs
    
    print(f"{section_name:30s}: {avg_time*1000:6.3f} ms/次 ({total_time*1000:7.1f} ms总计)")
    return avg_time


def test_vehicle_parameters(inp):
    """测试车辆参数计算部分"""
    # 车轮转速 (rad/s)
    wv = inp.W[1] / 0.324
    # 车轮角加速度 (rad/s^2)
    dwv = inp.W[2] / 0.324
    # 车轮扭矩 (Nm)
    Tv = (100.95 + 0.7667 * (inp.W[1] * 3.6) +
          0.0271 * (inp.W[1] * 3.6)**2 + 1820 * inp.W[2]) * 0.324
    return wv, dwv, Tv


def test_transmission_system(inp):
    """测试传动系统计算部分"""
    ateff = 0.94
    p3eff = 0.96
    r_gear = np.array([2.75, 2.75, 2.75, 2.75])
    p1_gear = 1.0
    p3_gear = 10.03
    
    # 串联充电功率
    Pchrg = (1 - inp.U[1]) * 10000
    Pchrg = Pchrg * (Pchrg > 10000)
    
    # 发动机工作点数据
    oolspd = np.pi/30 * np.array([0, 800, 1000, 1200, 1400, 1600, 1800, 2000,
                                  2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800])
    ooltrq = np.array([0, 90, 100, 100, 110, 100, 100, 100, 100, 110, 110, 110, 110, 100, 100, 100, 100])
    oolpwr = 1000 * np.array([0.00, 7.54, 10.47, 12.57, 16.13, 16.76, 18.85, 20.94,
                              23.04, 27.65, 29.95, 32.25, 34.56, 33.51, 35.61, 37.70, 40])
    
    # 曲轴转速 (rad/s) - scipy插值
    wv = inp.W[1] / 0.324
    ool_spd_interp = interp1d(oolpwr, oolspd, kind='linear',
                              bounds_error=False, fill_value='extrapolate')
    wg = ((inp.U[1] != 1) * (inp.U[2] == 0) *
          ool_spd_interp(Pchrg) +
          (inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
          r_gear[0] * wv)
    
    return wg


def test_engine_efficiency_interpolation():
    """测试发动机效率插值部分"""
    # 发动机效率矩阵 (简化版)
    we_list = np.pi/30 * np.concatenate([[0], np.arange(800, 4001, 200)])
    Te_list = np.arange(0, 141, 10)
    eta = 0.01 * np.random.random((15, 18))  # 简化的效率矩阵
    
    # 创建插值器
    eta_interp = RegularGridInterpolator((Te_list, we_list), eta,
                                         bounds_error=False, fill_value=None)
    
    # 测试查询
    Te = 50.0
    wg = 150.0
    query_points = np.column_stack([np.array([Te]).flatten(), 
                                   np.array([wg]).flatten()])
    e_th = eta_interp(query_points)
    
    return e_th


def test_motor_efficiency_interpolation():
    """测试电机效率插值部分"""
    # P1电机数据 (简化版)
    wm1_list = np.pi/30 * np.arange(0, 6001, 500)
    Tm1_list = np.concatenate([np.arange(-150, -19, 10), [-15, -10, -5, 5, 10, 15],
                               np.arange(20, 151, 10)])
    etam1 = 0.01 * np.random.random((34, 13))  # 简化的效率矩阵
    
    # 创建插值器
    etam1_interp = RegularGridInterpolator((Tm1_list, wm1_list), etam1,
                                           bounds_error=False, fill_value=None)
    
    # 测试查询
    Tm1 = 50.0
    wm1 = 1000.0
    query_points = np.column_stack([np.array([Tm1]).flatten(), 
                                   np.array([wm1]).flatten()])
    e1 = etam1_interp(query_points)
    
    return e1


def test_battery_calculations(inp):
    """测试电池计算部分"""
    # 电池参数
    soc_list = np.arange(0, 1.1, 0.1)
    R_dis = np.array([0.25, 0.2443, 0.1835, 0.1522, 0.1428, 0.1405, 0.1406,
                      0.1426, 0.1428, 0.1421, 0.1410])
    R_chg = np.array([0.14, 0.1390, 0.1282, 0.1259, 0.1304, 0.1352, 0.1356,
                      0.1349, 0.1339, 0.1340, 0.1349])
    V_oc = np.array([326.1, 336.2, 343.1, 347.3, 349.7, 353.2, 360.5,
                     368.2, 377.5, 387.8, 401.3])
    
    # 假设电功率消耗
    Pm = 5000.0  # 5kW
    
    # 电池效率
    e = np.where(Pm > 0, 1, 0.98)
    
    # 电池内阻插值
    r_dis_interp = interp1d(soc_list, R_dis, kind='linear',
                            bounds_error=False, fill_value='extrapolate')
    r_chg_interp = interp1d(soc_list, R_chg, kind='linear',
                            bounds_error=False, fill_value='extrapolate')
    r = np.where(Pm > 0,
                 r_dis_interp(inp.X[1]),
                 r_chg_interp(inp.X[1]))
    
    # 电池电压插值
    v_oc_interp = interp1d(soc_list, V_oc, kind='linear',
                           bounds_error=False, fill_value='extrapolate')
    v = v_oc_interp(inp.X[1])
    
    # 电池电流
    Ib = e * (v - np.sqrt(v**2 - 4 * r * Pm)) / (2 * r)
    
    # 新的电池荷电状态
    X_new = -Ib / (60 * 3600) + inp.X[1]
    
    return X_new, Ib


def test_array_operations():
    """测试数组运算性能"""
    a = np.random.random(1000)
    b = np.random.random(1000)
    
    # 复杂数组运算
    result = (a > 0.5) * (b < 0.3) * np.sin(a) * np.cos(b) + np.where(a > b, a**2, b**2)
    return result


def test_interpolation_creation():
    """测试插值器创建的性能"""
    x_data = np.linspace(0, 100, 100)
    y_data = np.sin(x_data)
    
    # 创建插值器
    interp_func = interp1d(x_data, y_data, kind='linear',
                          bounds_error=False, fill_value='extrapolate')
    
    return interp_func


def main():
    """主函数"""
    print("PHEV HT21 SerIP1P3 详细性能分析")
    print("=" * 80)
    
    inp = create_test_input()
    
    print("\n各部分性能测试 (1000次调用):")
    print("-" * 80)
    
    # 测试各个部分的性能
    benchmark_section("车辆参数计算", test_vehicle_parameters, inp)
    benchmark_section("传动系统计算", test_transmission_system, inp)
    benchmark_section("发动机效率插值", test_engine_efficiency_interpolation)
    benchmark_section("电机效率插值", test_motor_efficiency_interpolation)
    benchmark_section("电池计算", test_battery_calculations, inp)
    benchmark_section("数组运算", test_array_operations)
    benchmark_section("插值器创建", test_interpolation_creation)
    
    print("\n插值操作详细分析:")
    print("-" * 80)
    
    # 分析不同插值方法的性能
    x_data = np.linspace(0, 100, 100)
    y_data = np.sin(x_data)
    
    # 测试interp1d创建时间
    start_time = time.perf_counter()
    for _ in range(1000):
        interp_func = interp1d(x_data, y_data, kind='linear',
                              bounds_error=False, fill_value='extrapolate')
    end_time = time.perf_counter()
    print(f"interp1d创建1000次: {(end_time-start_time)*1000:.3f} ms")
    
    # 测试interp1d调用时间
    interp_func = interp1d(x_data, y_data, kind='linear',
                          bounds_error=False, fill_value='extrapolate')
    start_time = time.perf_counter()
    for _ in range(1000):
        result = interp_func(50.0)
    end_time = time.perf_counter()
    print(f"interp1d调用1000次: {(end_time-start_time)*1000:.3f} ms")
    
    # 测试RegularGridInterpolator
    x_grid = np.linspace(0, 10, 20)
    y_grid = np.linspace(0, 10, 20)
    z_data = np.random.random((20, 20))
    
    start_time = time.perf_counter()
    for _ in range(100):
        interp_func = RegularGridInterpolator((x_grid, y_grid), z_data,
                                            bounds_error=False, fill_value=None)
    end_time = time.perf_counter()
    print(f"RegularGridInterpolator创建100次: {(end_time-start_time)*1000:.3f} ms")
    
    interp_func = RegularGridInterpolator((x_grid, y_grid), z_data,
                                        bounds_error=False, fill_value=None)
    start_time = time.perf_counter()
    for _ in range(1000):
        query_points = np.array([[5.0, 5.0]])
        result = interp_func(query_points)
    end_time = time.perf_counter()
    print(f"RegularGridInterpolator调用1000次: {(end_time-start_time)*1000:.3f} ms")


if __name__ == "__main__":
    main()
