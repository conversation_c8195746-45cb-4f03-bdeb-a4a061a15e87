#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能分析脚本 - 分析 phev_ht21_serip1p3.py 函数的性能瓶颈
"""

import numpy as np
import time
import cProfile
import pstats
import io
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input():
    """创建测试输入数据"""
    inp = Input()
    
    # 状态变量 - 电池荷电状态
    inp.X = {1: 0.5}  # SOC = 50%
    
    # 控制变量
    inp.U = {1: 0.5, 2: 1}  # 扭矩分配比例和工作模式
    
    # 扰动变量 - 车速和加速度
    inp.W = {1: 15.0, 2: 0.5}  # 15 m/s, 0.5 m/s²
    
    # 采样时间
    inp.Ts = 1.0
    
    return inp


def benchmark_single_call():
    """测试单次函数调用的性能"""
    inp = create_test_input()
    
    # 预热
    for _ in range(10):
        phev_ht21_serip1p3(inp)
    
    # 测试单次调用时间
    start_time = time.perf_counter()
    X, C, I, out = phev_ht21_serip1p3(inp)
    end_time = time.perf_counter()
    
    single_call_time = end_time - start_time
    print(f"单次函数调用时间: {single_call_time*1000:.3f} ms")
    
    return single_call_time


def benchmark_multiple_calls(num_calls=1000):
    """测试多次函数调用的性能"""
    inp = create_test_input()
    
    print(f"测试 {num_calls} 次函数调用...")
    
    start_time = time.perf_counter()
    for i in range(num_calls):
        # 稍微变化输入参数模拟实际使用
        inp.U[1] = 0.5 + 0.1 * np.sin(i * 0.1)
        inp.W[1] = 15.0 + 5.0 * np.sin(i * 0.05)
        inp.W[2] = 0.5 * np.cos(i * 0.03)
        
        X, C, I, out = phev_ht21_serip1p3(inp)
    
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    avg_time = total_time / num_calls
    
    print(f"总时间: {total_time:.3f} 秒")
    print(f"平均每次调用: {avg_time*1000:.3f} ms")
    print(f"每秒可调用次数: {1/avg_time:.0f}")
    
    return avg_time


def profile_function():
    """使用cProfile分析函数性能"""
    inp = create_test_input()
    
    # 创建性能分析器
    pr = cProfile.Profile()
    
    # 开始分析
    pr.enable()
    
    # 运行多次函数调用
    for i in range(100):
        inp.U[1] = 0.5 + 0.1 * np.sin(i * 0.1)
        inp.W[1] = 15.0 + 5.0 * np.sin(i * 0.05)
        X, C, I, out = phev_ht21_serip1p3(inp)
    
    # 停止分析
    pr.disable()
    
    # 输出结果
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # 显示前20个最耗时的函数
    
    print("性能分析结果 (前20个最耗时的函数):")
    print("=" * 80)
    print(s.getvalue())


def analyze_interpolation_performance():
    """分析插值操作的性能"""
    inp = create_test_input()
    
    print("分析插值操作性能...")
    
    # 测试scipy插值函数的性能
    from scipy.interpolate import interp1d, RegularGridInterpolator
    
    # 创建测试数据
    x_data = np.linspace(0, 100, 100)
    y_data = np.sin(x_data)
    
    # 测试interp1d性能
    start_time = time.perf_counter()
    for _ in range(1000):
        interp_func = interp1d(x_data, y_data, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
        result = interp_func(50.0)
    end_time = time.perf_counter()
    
    print(f"interp1d创建+调用1000次: {(end_time-start_time)*1000:.3f} ms")
    
    # 测试预创建插值函数的性能
    interp_func = interp1d(x_data, y_data, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    
    start_time = time.perf_counter()
    for _ in range(1000):
        result = interp_func(50.0 + np.random.random())
    end_time = time.perf_counter()
    
    print(f"预创建interp1d调用1000次: {(end_time-start_time)*1000:.3f} ms")
    
    # 测试RegularGridInterpolator性能
    x_grid = np.linspace(0, 10, 20)
    y_grid = np.linspace(0, 10, 20)
    z_data = np.random.random((20, 20))
    
    start_time = time.perf_counter()
    for _ in range(100):
        interp_func = RegularGridInterpolator((x_grid, y_grid), z_data,
                                            bounds_error=False, fill_value=None)
        query_points = np.array([[5.0, 5.0]])
        result = interp_func(query_points)
    end_time = time.perf_counter()
    
    print(f"RegularGridInterpolator创建+调用100次: {(end_time-start_time)*1000:.3f} ms")


def analyze_array_operations():
    """分析数组操作的性能"""
    print("分析数组操作性能...")
    
    # 测试不同大小的数组操作
    sizes = [1, 10, 100, 1000]
    
    for size in sizes:
        # 创建测试数组
        a = np.random.random(size)
        b = np.random.random(size)
        
        # 测试基本运算
        start_time = time.perf_counter()
        for _ in range(10000):
            c = a * b + np.sin(a) * np.cos(b)
        end_time = time.perf_counter()
        
        print(f"数组大小 {size:4d}: 基本运算10000次 {(end_time-start_time)*1000:.3f} ms")


def main():
    """主函数"""
    print("PHEV HT21 SerIP1P3 函数性能分析")
    print("=" * 80)
    
    # 1. 单次调用性能
    print("\n1. 单次调用性能测试")
    print("-" * 40)
    single_time = benchmark_single_call()
    
    # 2. 多次调用性能
    print("\n2. 多次调用性能测试")
    print("-" * 40)
    avg_time = benchmark_multiple_calls(1000)
    
    # 3. 性能分析
    print("\n3. 详细性能分析")
    print("-" * 40)
    profile_function()
    
    # 4. 插值性能分析
    print("\n4. 插值操作性能分析")
    print("-" * 40)
    analyze_interpolation_performance()
    
    # 5. 数组操作性能分析
    print("\n5. 数组操作性能分析")
    print("-" * 40)
    analyze_array_operations()
    
    # 6. 总结
    print("\n6. 性能总结")
    print("-" * 40)
    print(f"单次调用时间: {single_time*1000:.3f} ms")
    print(f"平均调用时间: {avg_time*1000:.3f} ms")
    
    # 估算在动态规划中的总耗时
    # 假设网格大小: SOC=401点, 扭矩分配=351点, 模式=3点, 时间步=1800
    grid_size = 401 * 351 * 3
    time_steps = 1800
    total_calls = grid_size * time_steps
    estimated_time = total_calls * avg_time
    
    print(f"\n动态规划估算:")
    print(f"网格大小: {grid_size:,} 点")
    print(f"时间步数: {time_steps}")
    print(f"总函数调用次数: {total_calls:,}")
    print(f"估算总耗时: {estimated_time:.1f} 秒 ({estimated_time/60:.1f} 分钟)")


if __name__ == "__main__":
    main()
