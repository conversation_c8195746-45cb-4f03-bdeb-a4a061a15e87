#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 phev_ht21_serip1p3 函数的计时分析功能
"""

import numpy as np
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input():
    """创建测试输入数据"""
    inp = Input()
    inp.X = {1: 0.5}  # SOC = 50%
    inp.U = {1: 0.5, 2: 1}  # 扭矩分配比例和工作模式
    inp.W = {1: 15.0, 2: 0.5}  # 15 m/s, 0.5 m/s²
    inp.Ts = 1.0
    return inp


def test_timing_analysis():
    """测试计时分析功能"""
    print("PHEV HT21 SerIP1P3 函数计时分析测试")
    print("=" * 80)
    
    inp = create_test_input()
    
    print("\n1. 启用计时分析的单次调用:")
    print("-" * 50)
    
    # 调用函数并启用计时分析
    X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
    
    print("\n2. 验证输出结果:")
    print("-" * 50)
    print(f"新SOC: {X[1]:.4f}")
    print(f"成本: {C[1]:.6f}")
    print(f"不可行标志: {I}")
    print(f"计时信息是否包含在输出中: {'timing' in out}")
    
    if 'timing' in out:
        timing_info = out['timing']
        print(f"计时信息包含 {len(timing_info)} 个部分")
        
        print("\n3. 计时信息详细分析:")
        print("-" * 50)
        total_time = timing_info.get('总计', 0)
        
        # 按耗时排序
        sections = [(k, v) for k, v in timing_info.items() if k != '总计']
        sections.sort(key=lambda x: x[1], reverse=True)
        
        print(f"{'排名':<4} {'部分':<20} {'耗时(ms)':<12} {'占比(%)':<8}")
        print("-" * 50)
        
        for i, (section, time_ms) in enumerate(sections, 1):
            percentage = (time_ms / total_time) * 100 if total_time > 0 else 0
            print(f"{i:<4} {section:<20} {time_ms:<12.3f} {percentage:<8.1f}")
        
        print("-" * 50)
        print(f"{'总计':<25} {total_time:<12.3f} {'100.0':<8}")
        
        # 分析插值操作
        interp_sections = [(k, v) for k, v in timing_info.items() if '插值' in k]
        if interp_sections:
            total_interp = sum(v for k, v in interp_sections)
            interp_percentage = (total_interp / total_time) * 100 if total_time > 0 else 0
            
            print(f"\n4. 插值操作分析:")
            print("-" * 50)
            print(f"插值操作总耗时: {total_interp:.3f}ms ({interp_percentage:.1f}%)")
            
            for section, time_ms in interp_sections:
                percentage = (time_ms / total_interp) * 100 if total_interp > 0 else 0
                print(f"  {section}: {time_ms:.3f}ms ({percentage:.1f}%)")


def test_multiple_calls_timing():
    """测试多次调用的计时分析"""
    print("\n\n5. 多次调用计时分析:")
    print("-" * 50)
    
    inp = create_test_input()
    num_calls = 10
    all_timings = []
    
    print(f"执行 {num_calls} 次调用，收集计时数据...")
    
    for i in range(num_calls):
        # 稍微变化输入参数
        inp.U[1] = 0.5 + 0.1 * np.sin(i * 0.1)
        inp.W[1] = 15.0 + 5.0 * np.sin(i * 0.05)
        inp.W[2] = 0.5 * np.cos(i * 0.03)
        
        X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=False)  # 关闭打印
        
        # 手动调用一次获取计时信息
        if i == 0:  # 只在第一次获取详细计时
            X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
            if 'timing' in out:
                all_timings.append(out['timing'])
    
    if all_timings:
        print(f"\n统计分析 (基于 {len(all_timings)} 次调用):")
        print("-" * 50)
        
        # 计算平均值
        timing_data = all_timings[0]  # 使用第一次的数据作为示例
        
        print("各部分平均耗时:")
        for section, time_ms in timing_data.items():
            if section != '总计':
                print(f"  {section}: {time_ms:.3f}ms")
        
        total_avg = timing_data.get('总计', 0)
        print(f"  总计: {total_avg:.3f}ms")


def test_different_scenarios():
    """测试不同场景下的计时"""
    print("\n\n6. 不同场景计时对比:")
    print("-" * 50)
    
    scenarios = [
        {"name": "纯电动模式", "U1": 1.0, "U2": 0, "speed": 15.0, "accel": 0.0},
        {"name": "串联模式", "U1": 0.5, "U2": 0, "speed": 20.0, "accel": 0.5},
        {"name": "并联模式", "U1": 0.3, "U2": 1, "speed": 25.0, "accel": -0.2},
        {"name": "混合模式", "U1": 0.7, "U2": 2, "speed": 30.0, "accel": 0.8},
    ]
    
    for scenario in scenarios:
        inp = create_test_input()
        inp.U[1] = scenario["U1"]
        inp.U[2] = scenario["U2"]
        inp.W[1] = scenario["speed"]
        inp.W[2] = scenario["accel"]
        
        print(f"\n{scenario['name']}:")
        X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)


def main():
    """主函数"""
    # 测试基本计时功能
    test_timing_analysis()
    
    # 测试多次调用
    test_multiple_calls_timing()
    
    # 测试不同场景
    test_different_scenarios()
    
    print("\n" + "=" * 80)
    print("计时分析测试完成！")
    print("现在可以使用 debug_timing=True 参数来分析函数各部分的性能瓶颈")
    print("=" * 80)


if __name__ == "__main__":
    main()
