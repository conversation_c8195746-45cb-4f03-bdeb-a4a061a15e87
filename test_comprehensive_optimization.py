#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全面优化后的 phev_ht21_serip1p3 函数性能
"""

import numpy as np
import time
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input(soc=0.5, u1=0.5, u2=1, speed=15.0, accel=0.5):
    """创建测试输入数据"""
    inp = Input()
    inp.X = {1: soc}
    inp.U = {1: u1, 2: u2}
    inp.W = {1: speed, 2: accel}
    inp.Ts = 1.0
    return inp


def test_comprehensive_performance():
    """测试全面优化后的性能"""
    print("全面优化后的 PHEV HT21 SerIP1P3 函数性能测试")
    print("=" * 80)
    
    inp = create_test_input()
    
    # 1. 单次调用性能测试
    print("\n1. 单次调用性能测试")
    print("-" * 40)
    
    # 预热
    for _ in range(10):
        phev_ht21_serip1p3(inp, debug_timing=False)
    
    # 单次调用测试
    start_time = time.perf_counter()
    X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
    single_time = time.perf_counter() - start_time
    
    print(f"单次调用时间: {single_time*1000:.3f} ms")
    
    # 2. 多次调用性能测试
    print("\n2. 多次调用性能测试")
    print("-" * 40)
    
    num_calls = 1000
    start_time = time.perf_counter()
    for i in range(num_calls):
        # 变化输入参数模拟实际使用
        inp.U[1] = 0.5 + 0.1 * np.sin(i * 0.1)
        inp.W[1] = 15.0 + 5.0 * np.sin(i * 0.05)
        inp.W[2] = 0.5 * np.cos(i * 0.03)
        
        X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=False)
    
    total_time = time.perf_counter() - start_time
    avg_time = total_time / num_calls
    
    print(f"总时间 ({num_calls} 次调用): {total_time:.3f} 秒")
    print(f"平均每次调用: {avg_time*1000:.3f} ms")
    print(f"每秒可调用次数: {1/avg_time:.0f}")
    
    return single_time, avg_time


def analyze_timing_breakdown():
    """分析各部分耗时分布"""
    print("\n3. 各部分耗时分析")
    print("-" * 40)
    
    inp = create_test_input()
    X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
    
    if 'timing' in out:
        timing_info = out['timing']
        total_time = timing_info.get('总计', 0)
        
        print(f"{'部分':<20} {'耗时(ms)':<12} {'占比(%)':<8}")
        print("-" * 45)
        
        # 按耗时排序
        sections = [(k, v) for k, v in timing_info.items() if k != '总计']
        sections.sort(key=lambda x: x[1], reverse=True)
        
        for section, time_ms in sections:
            percentage = (time_ms / total_time) * 100 if total_time > 0 else 0
            print(f"{section:<20} {time_ms:<12.3f} {percentage:<8.1f}")
        
        print("-" * 45)
        print(f"{'总计':<20} {total_time:<12.3f} {'100.0':<8}")
        
        # 分析优化效果
        torque_allocation_time = timing_info.get('扭矩分配', 0)
        torque_percentage = (torque_allocation_time / total_time) * 100 if total_time > 0 else 0
        
        print(f"\n优化效果分析:")
        print(f"  扭矩分配耗时: {torque_allocation_time:.3f} ms ({torque_percentage:.1f}%)")
        
        if torque_percentage < 20:
            print("  ✅ 扭矩分配优化效果显著！")
        elif torque_percentage < 30:
            print("  ⚠️  扭矩分配仍有优化空间")
        else:
            print("  ❌ 扭矩分配需要进一步优化")


def test_different_scenarios():
    """测试不同场景的性能"""
    print("\n4. 不同场景性能测试")
    print("-" * 40)
    
    scenarios = [
        {"name": "纯电动模式", "soc": 0.8, "u1": 1.0, "u2": 0, "speed": 15.0, "accel": 0.0},
        {"name": "串联模式", "soc": 0.3, "u1": 0.5, "u2": 0, "speed": 20.0, "accel": 0.5},
        {"name": "并联模式", "soc": 0.6, "u1": 0.3, "u2": 1, "speed": 25.0, "accel": -0.2},
        {"name": "混合模式", "soc": 0.4, "u1": 0.7, "u2": 2, "speed": 30.0, "accel": 0.8},
    ]
    
    print(f"{'场景':<12} {'耗时(ms)':<12} {'扭矩分配(ms)':<15} {'占比(%)':<8}")
    print("-" * 55)
    
    for scenario in scenarios:
        inp = create_test_input(
            soc=scenario["soc"], 
            u1=scenario["u1"], 
            u2=scenario["u2"], 
            speed=scenario["speed"], 
            accel=scenario["accel"]
        )
        
        # 测试性能
        start_time = time.perf_counter()
        X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
        total_time = (time.perf_counter() - start_time) * 1000
        
        # 获取扭矩分配时间
        torque_time = 0
        if 'timing' in out:
            torque_time = out['timing'].get('扭矩分配', 0)
        
        percentage = (torque_time / total_time) * 100 if total_time > 0 else 0
        
        print(f"{scenario['name']:<12} {total_time:<12.3f} {torque_time:<15.3f} {percentage:<8.1f}")


def compare_optimization_levels():
    """对比不同优化级别的性能"""
    print("\n5. 优化效果对比")
    print("-" * 40)
    
    # 基准数据 (估算的优化前性能)
    baseline_data = {
        "原始版本": {"single": 1.2, "avg": 1.3, "torque_pct": 35},
        "插值优化版本": {"single": 0.8, "avg": 0.9, "torque_pct": 25},
        "扭矩分配优化版本": {"single": 0.6, "avg": 0.7, "torque_pct": 15},
    }
    
    # 当前性能
    single_time, avg_time = test_comprehensive_performance()
    
    inp = create_test_input()
    X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=True)
    
    current_torque_pct = 0
    if 'timing' in out:
        timing_info = out['timing']
        total_time = timing_info.get('总计', 0)
        torque_time = timing_info.get('扭矩分配', 0)
        current_torque_pct = (torque_time / total_time) * 100 if total_time > 0 else 0
    
    print(f"\n性能对比表:")
    print(f"{'版本':<20} {'单次(ms)':<12} {'平均(ms)':<12} {'扭矩占比(%)':<12} {'提升倍数':<10}")
    print("-" * 75)
    
    for version, data in baseline_data.items():
        improvement = data["avg"] / (avg_time * 1000) if avg_time > 0 else 1
        print(f"{version:<20} {data['single']:<12.1f} {data['avg']:<12.1f} "
              f"{data['torque_pct']:<12.1f} {improvement:<10.2f}x")
    
    print(f"{'当前全面优化版本':<20} {single_time*1000:<12.1f} {avg_time*1000:<12.1f} "
          f"{current_torque_pct:<12.1f} {'基准':<10}")


def estimate_dynamic_programming_performance():
    """估算动态规划性能"""
    print("\n6. 动态规划性能估算")
    print("-" * 40)
    
    # 获取当前平均性能
    inp = create_test_input()
    num_test_calls = 100
    
    start_time = time.perf_counter()
    for _ in range(num_test_calls):
        X, C, I, out = phev_ht21_serip1p3(inp, debug_timing=False)
    total_time = time.perf_counter() - start_time
    avg_time = total_time / num_test_calls
    
    # 动态规划参数
    soc_points = 401
    torque_points = 351
    mode_points = 3
    time_steps = 1800
    
    grid_size = soc_points * torque_points * mode_points
    total_calls = grid_size * time_steps
    estimated_time = total_calls * avg_time
    
    print(f"网格配置:")
    print(f"  SOC点数: {soc_points}")
    print(f"  扭矩分配点数: {torque_points}")
    print(f"  工作模式数: {mode_points}")
    print(f"  时间步数: {time_steps}")
    print(f"  网格大小: {grid_size:,} 点")
    print(f"  总函数调用次数: {total_calls:,}")
    
    print(f"\n性能估算:")
    print(f"  平均单次调用时间: {avg_time*1000:.3f} ms")
    print(f"  估算总耗时: {estimated_time:.1f} 秒")
    print(f"  估算总耗时: {estimated_time/60:.1f} 分钟")
    print(f"  估算总耗时: {estimated_time/3600:.1f} 小时")
    
    # 与基准对比
    baseline_time_hours = 245  # 原始估算
    improvement = baseline_time_hours / (estimated_time / 3600)
    
    print(f"\n对比原始版本:")
    print(f"  原始估算: {baseline_time_hours} 小时")
    print(f"  优化后估算: {estimated_time/3600:.1f} 小时")
    print(f"  性能提升: {improvement:.1f}x")
    
    if estimated_time / 3600 < 24:
        print("  🎉 优化效果显著！可在1天内完成")
    elif estimated_time / 3600 < 72:
        print("  ✅ 优化效果良好！可在3天内完成")
    else:
        print("  ⚠️  仍需进一步优化")


def main():
    """主函数"""
    print("开始全面优化性能测试")
    print("=" * 100)
    
    # 综合性能测试
    single_time, avg_time = test_comprehensive_performance()
    
    # 各部分耗时分析
    analyze_timing_breakdown()
    
    # 不同场景测试
    test_different_scenarios()
    
    # 优化效果对比
    compare_optimization_levels()
    
    # 动态规划性能估算
    estimate_dynamic_programming_performance()
    
    print("\n" + "=" * 100)
    print("🎉 全面优化测试完成！")
    print("主要优化措施:")
    print("  ✅ 缓存字典访问和数组访问")
    print("  ✅ 预计算条件标志和常数")
    print("  ✅ 提取公共子表达式")
    print("  ✅ 减少重复插值计算")
    print("  ✅ 优化数组运算逻辑")
    print("  ✅ 使用numpy.interp和RectBivariateSpline")
    print("=" * 100)


if __name__ == "__main__":
    main()
