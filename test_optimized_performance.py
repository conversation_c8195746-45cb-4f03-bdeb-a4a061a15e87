#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的 phev_ht21_serip1p3 函数性能
"""

import numpy as np
import time
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input():
    """创建测试输入数据"""
    inp = Input()
    inp.X = {1: 0.5}  # SOC = 50%
    inp.U = {1: 0.5, 2: 1}  # 扭矩分配比例和工作模式
    inp.W = {1: 15.0, 2: 0.5}  # 15 m/s, 0.5 m/s²
    inp.Ts = 1.0
    return inp


def benchmark_optimized_function():
    """测试优化后函数的性能"""
    inp = create_test_input()
    
    print("优化后的 PHEV HT21 SerIP1P3 函数性能测试")
    print("=" * 60)
    
    # 预热
    print("预热中...")
    for _ in range(10):
        phev_ht21_serip1p3(inp)
    
    # 测试单次调用时间
    print("\n1. 单次调用性能测试")
    print("-" * 30)
    start_time = time.perf_counter()
    X, C, I, out = phev_ht21_serip1p3(inp)
    end_time = time.perf_counter()
    
    single_call_time = end_time - start_time
    print(f"单次函数调用时间: {single_call_time*1000:.3f} ms")
    
    # 测试多次调用性能
    print("\n2. 多次调用性能测试")
    print("-" * 30)
    num_calls = 1000
    
    start_time = time.perf_counter()
    for i in range(num_calls):
        # 稍微变化输入参数模拟实际使用
        inp.U[1] = 0.5 + 0.1 * np.sin(i * 0.1)
        inp.W[1] = 15.0 + 5.0 * np.sin(i * 0.05)
        inp.W[2] = 0.5 * np.cos(i * 0.03)
        
        X, C, I, out = phev_ht21_serip1p3(inp)
    
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    avg_time = total_time / num_calls
    
    print(f"总时间 ({num_calls} 次调用): {total_time:.3f} 秒")
    print(f"平均每次调用: {avg_time*1000:.3f} ms")
    print(f"每秒可调用次数: {1/avg_time:.0f}")
    
    # 估算动态规划性能
    print("\n3. 动态规划性能估算")
    print("-" * 30)
    
    # 网格配置
    soc_points = 401
    torque_points = 351
    mode_points = 3
    time_steps = 1800
    
    grid_size = soc_points * torque_points * mode_points
    total_calls = grid_size * time_steps
    estimated_time = total_calls * avg_time
    
    print(f"网格大小: {grid_size:,} 点")
    print(f"时间步数: {time_steps}")
    print(f"总函数调用次数: {total_calls:,}")
    print(f"估算总耗时: {estimated_time:.1f} 秒")
    print(f"估算总耗时: {estimated_time/60:.1f} 分钟")
    print(f"估算总耗时: {estimated_time/3600:.1f} 小时")
    
    return single_call_time, avg_time


def compare_with_baseline():
    """与基准性能对比"""
    print("\n4. 性能对比")
    print("-" * 30)
    
    # 基准性能数据 (优化前)
    baseline_single = 1.021  # ms
    baseline_avg = 1.163     # ms
    baseline_total_hours = 245  # 小时
    
    # 当前性能
    single_time, avg_time = benchmark_optimized_function()
    
    # 计算提升倍数
    single_improvement = baseline_single / (single_time * 1000)
    avg_improvement = baseline_avg / (avg_time * 1000)
    time_improvement = baseline_total_hours / (760055400 * avg_time / 3600)
    
    print(f"\n性能提升对比:")
    print(f"单次调用提升: {single_improvement:.2f}x")
    print(f"平均调用提升: {avg_improvement:.2f}x")
    print(f"动态规划总时间提升: {time_improvement:.2f}x")
    
    # 新的动态规划时间估算
    new_total_hours = baseline_total_hours / avg_improvement
    print(f"\n优化后动态规划预计耗时: {new_total_hours:.1f} 小时")
    
    if new_total_hours < 24:
        print("✅ 优化效果显著！动态规划可在1天内完成")
    elif new_total_hours < 72:
        print("✅ 优化效果良好！动态规划可在3天内完成")
    elif new_total_hours < 168:
        print("⚠️  优化有效果，动态规划可在1周内完成")
    else:
        print("❌ 仍需进一步优化")


def test_function_correctness():
    """测试函数正确性"""
    print("\n5. 函数正确性测试")
    print("-" * 30)
    
    inp = create_test_input()
    
    try:
        X, C, I, out = phev_ht21_serip1p3(inp)
        
        # 检查输出格式
        assert isinstance(X, dict), "X 应该是字典类型"
        assert 1 in X, "X 应该包含键 1"
        assert isinstance(X[1], (int, float, np.number)), "X[1] 应该是数值类型"
        
        assert isinstance(C, (int, float, np.number)), "C 应该是数值类型"
        assert isinstance(I, (int, float, np.number)), "I 应该是数值类型"
        
        # 检查数值合理性
        assert 0 <= X[1] <= 1, f"SOC 应该在 [0,1] 范围内，实际值: {X[1]}"
        assert C >= 0, f"成本应该非负，实际值: {C}"
        assert I in [0, 1], f"不可行标志应该是 0 或 1，实际值: {I}"
        
        print("✅ 函数输出格式正确")
        print(f"   新SOC: {X[1]:.4f}")
        print(f"   成本: {C:.4f}")
        print(f"   不可行标志: {I}")
        
        # 测试不同输入条件
        test_cases = [
            {"SOC": 0.2, "U1": 0.3, "U2": 0, "speed": 10.0, "accel": 0.2},
            {"SOC": 0.8, "U1": 0.7, "U2": 1, "speed": 20.0, "accel": -0.1},
            {"SOC": 0.5, "U1": 1.0, "U2": 2, "speed": 25.0, "accel": 0.8},
        ]
        
        print("\n   测试不同输入条件:")
        for i, case in enumerate(test_cases):
            inp.X[1] = case["SOC"]
            inp.U[1] = case["U1"]
            inp.U[2] = case["U2"]
            inp.W[1] = case["speed"]
            inp.W[2] = case["accel"]
            
            X, C, I, out = phev_ht21_serip1p3(inp)
            print(f"   案例 {i+1}: SOC {case['SOC']:.1f} -> {X[1]:.3f}, 成本 {C:.2f}, 可行性 {I}")
        
        print("✅ 所有测试案例通过")
        
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("开始测试优化后的 phev_ht21_serip1p3 函数")
    print("=" * 80)
    
    # 测试函数正确性
    if not test_function_correctness():
        print("函数正确性测试失败，停止性能测试")
        return
    
    # 性能测试
    benchmark_optimized_function()
    
    # 性能对比
    compare_with_baseline()
    
    print("\n" + "=" * 80)
    print("测试完成！")


if __name__ == "__main__":
    main()
