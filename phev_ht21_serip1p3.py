#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合动力车辆模型函数 - PHEV HT21 Series P1P3

优化版本：预创建插值函数以提高性能
"""

import numpy as np
from scipy.interpolate import RegularGridInterpolator
from dpm import Input, Output

# ============================================================================
# 全局预创建插值函数 - 性能优化
# ============================================================================

# 发动机工作点数据
_oolspd = np.pi/30 * np.array([0, 800, 1000, 1200, 1400, 1600, 1800, 2000,
                              2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800])
_ooltrq = np.array([0, 90, 100, 100, 110, 100, 100, 100, 100, 110, 110, 110, 110, 100, 100, 100, 100])
_oolpwr = 1000 * np.array([0.00, 7.54, 10.47, 12.57, 16.13, 16.76, 18.85, 20.94,
                          23.04, 27.65, 29.95, 32.25, 34.56, 33.51, 35.61, 37.70, 40])

# 预创建发动机工作点插值函数 - 使用numpy.interp优化
# 注意：numpy.interp 比 scipy.interp1d 快约13倍

# 发动机效率数据
_we_list = np.pi/30 * np.concatenate([[0], np.arange(800, 4001, 200)])
_Te_list = np.arange(0, 141, 10)
_eta = 0.01 * np.array([
    [0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10, 0.10],
    [0.10, 18.84, 18.84, 19.26, 19.48, 19.48, 19.66, 19.46, 19.14, 19.13, 19.13, 18.70, 18.31, 18.02, 17.54, 17.54, 15.41, 14.99],
    [0.10, 26.41, 23.99, 24.43, 24.71, 24.90, 24.97, 24.78, 24.31, 24.14, 23.96, 23.76, 23.59, 23.26, 22.82, 21.68, 20.27, 20.03],
    [0.10, 30.76, 28.68, 29.03, 29.21, 29.29, 29.25, 29.03, 28.55, 28.44, 28.30, 28.24, 28.38, 27.93, 27.39, 26.28, 25.17, 24.90],
    [0.10, 30.76, 31.69, 31.80, 31.81, 31.71, 31.68, 31.59, 31.35, 31.23, 31.31, 31.35, 31.43, 30.94, 30.54, 29.76, 28.74, 28.49],
    [0.10, 33.32, 33.61, 33.63, 33.55, 33.49, 33.57, 33.62, 33.48, 33.64, 33.70, 33.71, 33.50, 33.23, 33.10, 32.40, 31.45, 31.27],
    [0.10, 34.77, 34.81, 35.10, 35.07, 35.07, 35.15, 35.11, 35.11, 35.25, 35.53, 35.69, 35.30, 35.29, 35.14, 34.42, 33.48, 33.28],
    [0.10, 35.80, 35.68, 36.20, 36.62, 36.99, 37.20, 37.35, 37.33, 37.43, 37.48, 37.23, 36.91, 36.97, 36.68, 36.04, 35.40, 34.24],
    [0.10, 36.61, 36.39, 37.54, 38.20, 38.45, 38.54, 38.71, 38.68, 38.73, 38.64, 38.38, 38.13, 38.17, 37.75, 37.13, 36.46, 34.89],
    [0.10, 38.28, 37.55, 38.55, 38.87, 39.05, 39.21, 39.61, 39.54, 39.56, 39.51, 39.21, 38.96, 39.09, 38.75, 38.39, 37.86, 36.89],
    [0.10, 38.28, 38.28, 38.81, 38.75, 39.25, 39.48, 39.89, 39.93, 40.17, 40.16, 39.73, 39.51, 39.61, 39.36, 39.12, 38.20, 37.70],
    [0.10, 38.28, 38.28, 38.81, 39.17, 39.17, 38.80, 39.37, 39.91, 40.26, 40.16, 39.83, 39.62, 39.27, 38.91, 38.21, 37.23, 36.93],
    [0.10, 38.28, 38.28, 38.81, 39.17, 39.17, 39.37, 39.74, 39.04, 39.52, 39.52, 39.37, 39.08, 38.45, 38.75, 38.75, 37.59, 37.20],
    [0.10, 38.28, 38.28, 38.81, 39.17, 39.17, 39.37, 39.74, 39.04, 39.52, 39.52, 39.37, 39.08, 38.45, 38.75, 38.75, 37.59, 37.20],
    [0.10, 38.28, 38.28, 38.81, 39.17, 39.17, 39.37, 39.74, 39.04, 39.52, 39.52, 39.37, 39.08, 38.45, 38.75, 38.75, 37.59, 37.20]
])

# 预创建发动机效率插值函数
_eta_interp = RegularGridInterpolator((_Te_list, _we_list), _eta,
                                     bounds_error=False, fill_value=None)

# 最大发动机扭矩数据
_we0_list = np.pi/30 * np.concatenate([[0], np.arange(1000, 6001, 500)])
_Tmax = np.array([50, 86.4, 104.7, 115, 119, 124.9, 124.3, 122.3, 127.9, 124.4, 120.8, 120])

# 预创建最大发动机扭矩插值函数 - 使用numpy.interp优化

# P1电机数据
_wm1_list = np.pi/30 * np.arange(0, 6001, 500)
_Tm1_list = np.concatenate([np.arange(-150, -19, 10), [-15, -10, -5, 5, 10, 15],
                           np.arange(20, 151, 10)])
_Tm1max = 0.1 * np.array([150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150])
_Tm1min = -1 * np.array([150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150])

# P1电机效率图
_etam1 = 0.01 * np.array([
    [67.11, 67.11, 81.56, 86.40, 88.85, 90.30, 91.29, 91.97, 92.46, 92.83, 93.25, 93.29, 93.00],
    [68.38, 68.38, 82.18, 86.80, 89.16, 90.54, 91.48, 92.14, 92.62, 92.96, 93.40, 93.42, 93.27],
    [69.67, 69.67, 82.79, 87.18, 89.43, 90.75, 91.64, 92.28, 92.74, 93.06, 93.48, 93.55, 93.46],
    [70.76, 70.76, 83.32, 87.51, 89.66, 90.91, 91.75, 92.36, 92.81, 93.10, 93.53, 93.65, 93.59],
    [71.74, 71.74, 83.74, 87.73, 89.82, 90.98, 91.83, 92.39, 92.83, 93.11, 93.51, 93.67, 93.63],
    [72.68, 72.68, 84.13, 87.93, 89.91, 91.02, 91.82, 92.38, 92.81, 93.06, 93.43, 93.60, 93.59],
    [73.42, 73.42, 84.39, 88.04, 89.96, 90.99, 91.74, 92.29, 92.68, 92.94, 93.28, 93.51, 93.47],
    [73.99, 73.99, 84.53, 88.07, 89.85, 90.87, 91.59, 92.09, 92.50, 92.71, 93.03, 93.27, 93.19],
    [74.38, 74.38, 84.51, 87.86, 89.61, 90.57, 91.22, 91.70, 92.08, 92.32, 92.64, 92.87, 92.87],
    [74.53, 74.53, 84.24, 87.51, 89.21, 90.09, 90.71, 91.19, 91.53, 91.73, 92.06, 92.35, 92.31],
    [74.24, 74.24, 83.69, 86.84, 88.50, 89.32, 89.90, 90.34, 90.69, 90.85, 91.06, 91.46, 91.40],
    [73.21, 73.21, 82.40, 85.48, 87.11, 87.88, 88.41, 88.83, 89.18, 89.30, 89.58, 89.95, 89.77],
    [70.92, 70.92, 80.04, 83.05, 84.61, 85.39, 85.88, 86.24, 86.54, 86.65, 86.98, 87.48, 87.17],
    [65.64, 65.64, 74.71, 77.97, 79.42, 80.07, 80.48, 80.76, 80.93, 80.85, 81.93, 82.01, 81.27],
    [59.98, 59.98, 69.31, 72.64, 73.98, 74.64, 75.06, 75.40, 75.62, 75.62, 75.71, 75.92, 74.96],
    [49.58, 49.58, 58.98, 62.32, 63.93, 64.47, 64.25, 64.21, 64.17, 64.02, 63.85, 64.24, 62.31],
    [19.61, 19.61, 26.13, 27.50, 27.48, 27.65, 29.50, 28.47, 29.62, 31.85, 21.35, 4.96, 4.96],
    [50.73, 50.73, 56.49, 58.87, 59.94, 60.53, 60.63, 61.04, 61.19, 61.82, 62.12, 62.97, 63.37],
    [63.47, 63.47, 69.51, 72.34, 73.25, 73.57, 74.11, 74.88, 74.85, 74.53, 75.15, 75.91, 76.15],
    [68.97, 68.97, 75.45, 78.14, 79.23, 79.94, 80.43, 80.97, 81.22, 81.52, 81.94, 82.17, 82.18],
    [72.72, 72.72, 79.23, 81.69, 82.92, 83.67, 84.00, 84.51, 84.72, 84.82, 85.55, 85.99, 85.97],
    [76.04, 76.04, 82.72, 85.37, 86.57, 87.31, 87.77, 88.25, 88.50, 88.62, 89.39, 89.89, 89.94],
    [77.63, 77.63, 84.46, 87.19, 88.44, 89.20, 89.73, 90.20, 90.42, 90.68, 91.45, 91.80, 91.78],
    [78.38, 78.38, 85.42, 88.24, 89.60, 90.40, 90.95, 91.37, 91.67, 91.99, 92.65, 92.95, 92.96],
    [78.61, 78.61, 85.87, 88.80, 90.17, 91.04, 91.63, 92.04, 92.37, 92.73, 93.43, 93.67, 93.65],
    [78.61, 78.61, 86.08, 89.12, 90.59, 91.48, 92.08, 92.54, 92.86, 93.28, 93.94, 94.13, 94.09],
    [78.37, 78.37, 86.13, 89.25, 90.80, 91.74, 92.36, 92.85, 93.18, 93.64, 94.29, 94.41, 94.33],
    [78.03, 78.03, 86.03, 89.24, 90.90, 91.85, 92.52, 93.02, 93.34, 93.92, 94.52, 94.56, 94.51],
    [77.57, 77.57, 85.87, 89.21, 90.88, 91.91, 92.61, 93.12, 93.45, 94.14, 94.64, 94.67, 94.56],
    [77.04, 77.04, 85.62, 89.04, 90.82, 91.90, 92.61, 93.14, 93.52, 94.29, 94.70, 94.69, 94.54],
    [76.48, 76.48, 85.33, 88.85, 90.72, 91.82, 92.57, 93.12, 93.50, 94.37, 94.67, 94.61, 94.42],
    [75.84, 75.84, 84.99, 88.60, 90.53, 91.69, 92.49, 93.08, 93.51, 94.37, 94.63, 94.48, 94.23],
    [75.10, 75.10, 84.58, 88.32, 90.34, 91.55, 92.38, 92.96, 93.48, 94.35, 94.52, 94.31, 93.92],
    [74.37, 74.37, 84.13, 88.00, 90.11, 91.36, 92.22, 92.82, 93.43, 94.27, 94.35, 94.05, 93.34]
])

# 预创建P1电机插值函数
_etam1_interp = RegularGridInterpolator((_Tm1_list, _wm1_list), _etam1,
                                       bounds_error=False, fill_value=None)
# P1电机扭矩限制插值函数 - 使用numpy.interp优化

# P3电机数据
_wm3_list = np.pi/30 * np.arange(0, 12001, 500)
_Tm3_list = np.arange(-180, 181, 10)
_Tm3max = np.array([180, 179.4, 179.7, 179.19, 178.94, 179.28, 178.82, 179.5, 178.23, 178.67,
                   159.55, 146.59, 127.88, 114.55, 102.8, 95, 86.84, 79.04, 74.9, 68.58,
                   64.3, 60.24, 56.58, 52.31, 50.11])
_Tm3min = -_Tm3max

# P3电机效率图 (简化显示，实际数据很长)
_etam3 = 0.01 * np.array([
    [1.60, 1.60, 41.40, 62.90, 70.40, 79.30, 80.00, 81.20, 84.10, 87.00, 83.70, 90.40, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [2.60, 2.60, 46.70, 66.20, 73.00, 76.60, 81.80, 81.80, 85.30, 87.80, 83.70, 90.40, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [14.60, 14.60, 51.30, 69.00, 75.10, 77.30, 83.30, 83.20, 86.30, 88.70, 83.70, 90.40, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [18.50, 18.50, 55.10, 71.30, 77.00, 80.60, 84.20, 83.30, 87.20, 89.70, 83.70, 90.40, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [23.80, 23.80, 57.70, 73.60, 78.20, 84.10, 85.20, 82.60, 88.00, 89.90, 84.60, 90.40, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [28.40, 28.40, 61.30, 75.10, 79.30, 86.80, 86.10, 87.60, 88.60, 89.80, 90.80, 91.10, 91.20, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [33.70, 33.70, 63.50, 76.30, 81.00, 88.10, 86.70, 88.00, 89.20, 89.80, 91.20, 91.40, 92.00, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [37.00, 37.00, 65.50, 77.60, 82.10, 87.40, 87.30, 88.70, 89.60, 90.00, 91.50, 91.90, 92.10, 92.10, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [40.30, 40.30, 67.30, 78.70, 82.90, 87.40, 87.90, 89.10, 90.10, 90.70, 91.70, 92.20, 92.80, 92.50, 92.20, 91.70, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [43.50, 43.50, 69.20, 79.50, 83.60, 87.50, 88.10, 89.40, 90.60, 91.30, 92.00, 92.40, 92.80, 93.00, 92.80, 92.10, 92.40, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [46.40, 46.40, 70.50, 80.40, 84.10, 87.80, 88.90, 89.80, 90.90, 91.70, 92.10, 92.40, 92.80, 93.20, 93.20, 91.80, 92.80, 92.80, 92.60, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [48.70, 48.70, 71.80, 81.10, 84.70, 88.10, 88.80, 90.10, 91.40, 92.10, 92.20, 92.40, 92.80, 93.20, 93.30, 92.00, 93.30, 92.90, 92.90, 92.80, 92.40, 92.50, 92.10, 87.10, 93.50],
    [50.30, 50.30, 72.80, 81.50, 85.00, 87.70, 89.00, 90.20, 91.30, 92.00, 92.10, 92.40, 92.80, 93.20, 93.30, 91.90, 93.60, 93.00, 93.20, 93.20, 85.60, 92.50, 92.10, 87.10, 93.50],
    [47.90, 47.90, 73.60, 81.90, 85.20, 87.70, 88.90, 90.00, 91.20, 91.60, 91.90, 91.70, 92.70, 92.10, 84.50, 92.20, 84.40, 95.10, 93.60, 93.30, 92.90, 92.70, 92.80, 92.70, 93.50],
    [53.10, 53.10, 73.90, 81.90, 85.20, 87.10, 88.50, 89.50, 90.70, 91.40, 91.60, 91.10, 92.20, 88.30, 80.90, 83.10, 81.40, 92.90, 93.40, 92.80, 92.90, 92.40, 93.10, 92.50, 93.20],
    [53.80, 53.80, 73.50, 81.10, 84.90, 86.70, 87.70, 88.90, 89.70, 90.60, 90.60, 92.20, 91.50, 82.80, 75.90, 76.70, 81.10, 89.20, 92.70, 92.40, 92.80, 92.30, 92.80, 92.50, 92.50],
    [51.50, 51.50, 71.30, 79.30, 83.30, 85.00, 86.30, 87.00, 87.50, 87.90, 90.00, 89.30, 89.60, 90.60, 67.70, 74.60, 78.80, 77.90, 79.00, 89.50, 92.80, 89.40, 88.00, 91.70, 89.70],
    [46.60, 46.60, 65.40, 72.80, 77.50, 79.20, 80.50, 82.00, 87.70, 85.60, 81.20, 86.70, 91.30, 91.30, 52.30, 47.90, 42.20, 47.30, 48.20, 80.10, 88.70, 86.60, 86.60, 47.50, 93.10],
    [57.61, 57.61, 72.13, 78.23, 81.31, 83.12, 83.91, 85.16, 88.23, 86.75, 85.45, 87.32, 91.24, 84.72, 59.73, 59.21, 64.31, 67.72, 66.14, 82.92, 87.99, 84.60, 74.40, 53.74, 78.16],
    [68.62, 68.62, 78.85, 83.66, 85.12, 87.03, 87.31, 88.31, 88.76, 87.89, 89.69, 87.94, 91.18, 78.14, 67.15, 70.52, 86.41, 88.14, 84.07, 85.73, 87.27, 82.60, 62.20, 59.98, 63.21],
    [70.02, 70.02, 80.97, 85.08, 87.75, 89.10, 90.23, 90.99, 91.35, 91.80, 92.10, 92.38, 92.18, 91.65, 91.79, 87.95, 84.92, 91.07, 90.86, 90.93, 92.94, 90.73, 91.91, 84.87, 89.46],
    [70.74, 70.74, 81.39, 85.44, 88.32, 89.71, 90.65, 91.79, 91.64, 92.97, 92.18, 93.32, 92.87, 94.02, 93.56, 95.74, 92.92, 92.40, 94.81, 93.09, 89.49, 92.76, 92.52, 93.45, 93.87],
    [70.32, 70.32, 80.87, 85.48, 88.42, 90.62, 91.85, 91.72, 92.62, 93.28, 93.40, 93.51, 94.29, 94.89, 94.72, 95.69, 93.82, 93.30, 92.43, 93.90, 93.13, 93.68, 93.49, 93.50, 93.06],
    [69.20, 69.20, 80.32, 85.17, 88.19, 89.57, 90.58, 91.91, 92.75, 93.50, 93.55, 94.20, 93.57, 94.24, 94.26, 96.62, 93.69, 93.31, 94.06, 94.11, 92.86, 92.99, 90.75, 92.25, 90.92],
    [68.20, 68.20, 79.85, 84.51, 88.02, 89.26, 90.66, 91.85, 92.41, 92.35, 93.60, 94.17, 94.43, 94.86, 94.69, 94.79, 93.90, 93.67, 94.18, 91.50, 94.25, 91.34, 90.75, 92.25, 90.92],
    [67.17, 67.17, 79.10, 83.83, 87.97, 88.90, 89.81, 91.52, 92.38, 93.75, 93.45, 94.41, 94.25, 94.22, 95.46, 93.82, 93.16, 90.81, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [66.17, 66.17, 78.14, 83.27, 87.37, 89.20, 89.28, 91.45, 92.12, 93.35, 93.12, 94.10, 93.36, 93.75, 94.12, 93.27, 91.41, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [65.00, 65.00, 77.27, 82.61, 86.69, 89.19, 88.94, 91.04, 91.81, 93.13, 93.53, 93.46, 93.67, 93.47, 92.88, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [65.23, 65.23, 76.15, 81.85, 86.32, 88.94, 90.28, 90.34, 91.77, 93.01, 93.30, 93.05, 93.38, 92.84, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [62.02, 62.02, 75.16, 80.98, 85.64, 89.26, 88.14, 89.92, 91.25, 92.27, 92.99, 93.23, 92.97, 90.86, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [60.32, 60.32, 73.91, 80.14, 85.03, 87.76, 87.88, 89.58, 90.92, 91.88, 91.91, 92.43, 90.63, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [58.66, 58.66, 72.71, 79.25, 84.22, 86.17, 88.05, 88.84, 90.29, 91.35, 92.03, 91.74, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [57.30, 57.30, 71.23, 77.87, 83.19, 85.66, 86.61, 88.52, 89.95, 91.79, 92.00, 89.69, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [55.20, 55.20, 69.77, 76.73, 82.19, 85.82, 86.21, 87.48, 91.12, 90.35, 90.61, 89.69, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [53.29, 53.29, 67.79, 75.06, 81.26, 84.42, 85.85, 86.70, 89.79, 89.09, 87.60, 89.69, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [50.62, 50.62, 65.65, 73.18, 79.69, 82.74, 84.69, 85.72, 88.66, 89.57, 87.60, 89.69, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92],
    [48.48, 48.48, 63.55, 71.56, 77.87, 82.21, 83.02, 84.59, 86.67, 87.82, 87.60, 89.69, 89.81, 91.47, 91.31, 92.38, 91.10, 91.00, 91.29, 91.02, 94.25, 91.34, 90.75, 92.25, 90.92]
])

# 预创建P3电机插值函数
_etam3_interp = RegularGridInterpolator((_Tm3_list, _wm3_list), _etam3,
                                       bounds_error=False, fill_value=None)
# P3电机扭矩限制插值函数 - 使用numpy.interp优化

# 电池数据
_soc_list = np.arange(0, 1.1, 0.1)
_R_dis = np.array([0.25, 0.2443, 0.1835, 0.1522, 0.1428, 0.1405, 0.1406,
                  0.1426, 0.1428, 0.1421, 0.1410])
_R_chg = np.array([0.14, 0.1390, 0.1282, 0.1259, 0.1304, 0.1352, 0.1356,
                  0.1349, 0.1339, 0.1340, 0.1349])
_V_oc = np.array([326.1, 336.2, 343.1, 347.3, 349.7, 353.2, 360.5,
                 368.2, 377.5, 387.8, 401.3])

# 预创建电池插值函数 - 使用numpy.interp优化


def phev_ht21_serip1p3(inp: Input, par=None):
    """
    混合动力车辆模型主函数

    参数:
        inp: Input对象
        par: 用户定义参数

    返回:
        X[0]: 结果电池荷电状态
        C[0]: 成本 (燃油消耗率)
        I: 不可行标志
        out: 用户定义输出
    """
    
    # 初始化输出
    out = {}
    
    # ========================================================================
    # 车辆参数
    # ========================================================================
    # 车轮半径 = 0.324 m
    # F0 / F1 / F2 = 100.95 / 0.7667 / 0.0271
    # 测试质量 = 1820 kg
    
    # 车轮转速 (rad/s)
    wv = inp.W[1] / 0.324
    # 车轮角加速度 (rad/s^2)
    dwv = inp.W[2] / 0.324
    # 车轮扭矩 (Nm)
    Tv = (100.95 + 0.7667 * (inp.W[1] * 3.6) +
          0.0271 * (inp.W[1] * 3.6)**2 + 1820 * inp.W[2]) * 0.324
    
    # ========================================================================
    # 传动系统
    # ========================================================================
    ateff = 0.94  # AT变速箱效率 = 0.94
    p3eff = 0.96  # P3齿轮效率 = 0.96
    
    # 齿轮比
    r_gear = np.array([2.75, 2.75, 2.75, 2.75])
    p1_gear = 1.0
    p3_gear = 10.03
    
    # 串联充电功率
    Pchrg = (1 - inp.U[1]) * 10000
    Pchrg = Pchrg * (Pchrg > 10000)

    # 曲轴转速 (rad/s) - 使用numpy.interp优化
    wg = ((inp.U[1] != 1) * (inp.U[2] == 0) *
          np.interp(Pchrg, _oolpwr, _oolspd) +
          (inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
          r_gear[0] * wv)

    # P1/P3电机转速 (rad/s)
    wm1 = (inp.U[1] != 1) * (p1_gear * wg)
    wm3 = p3_gear * wv

    # 曲轴角加速度 (rad/s^2)
    dwg = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
           r_gear[0] * dwv)

    # P1/P3电机角加速度 (rad/s^2)
    dwm1 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
            p1_gear * dwg)
    dwm3 = p3_gear * dwv
    
    # ========================================================================
    # 扭矩分配
    # ========================================================================
    # 发动机惯量 = 0.10 kg*m^2
    # 电机惯量 = 0.03 kg*m^2
    # 怠速转速 = 100 rad/s
    
    # 发动机拖拽扭矩列表 (保留用于注释)
    # Te0_list = np.array([0, 15, 16.3, 17.1, 18.3, 19.5, 21.8, 24.2, 26.3, 27.7, 29, 30.4])
    
    # 发动机拖拽扭矩 (Nm)
    Te0 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
           dwg * 0.10)

    # P1电机拖拽扭矩 (Nm)
    Tm10 = ((inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
            dwm1 * 0.03)
    # P3电机拖拽扭矩 (Nm)
    Tm30 = dwm3 * 0.03

    # 车轮总需求扭矩 (Nm)
    Ttotwhl = (((inp.U[1] == 1) + (inp.U[1] != 1) * (inp.U[2] == 0)) *
               (Tm30 * p3_gear + Tv) +
               (inp.U[1] != 1) * (inp.U[2] == 2) *
               ((Te0 + Tm10 * p1_gear) * r_gear[0] + Tm30 * p3_gear + Tv))

    Ttotclu = ((inp.U[1] != 1) * (inp.U[2] == 1) *
               ((Tm30 * p3_gear + Tv) / r_gear[0] *
                (((Tm30 * p3_gear + Tv) >= 0) / ateff +
                 ((Tm30 * p3_gear + Tv) < 0) * ateff) + Te0 + Tm10 * p1_gear))

    # 发动机提供的扭矩 - 使用numpy.interp优化
    Te = ((inp.U[1] != 1) *
          ((inp.U[2] == 0) * np.interp(Pchrg, _oolpwr, _ooltrq) +
           (inp.U[2] == 2) * ((Ttotwhl > 0) * (1 - inp.U[1]) *
                                 Ttotwhl / r_gear[0] / ateff + (Ttotwhl <= 0) * 0.1) +
           (inp.U[2] == 1) * ((Ttotclu > 0) * (1 - inp.U[1]) * Ttotclu +
                                 (Ttotclu <= 0) * 0.1)))

    # 电机提供的扭矩
    Tm1 = ((inp.U[1] != 1) * (inp.U[2] == 0) * (-1/p1_gear * Te) +
           (inp.U[1] != 1) * (inp.U[2] == 1) *
           (((Ttotclu > 0) + (wv > 9) * (Ttotclu <= 0)) * inp.U[1] * Ttotclu / p1_gear))

    Tm3 = (((inp.U[1] == 1) + (inp.U[1] != 1) * (inp.U[2] == 0)) *
           ((wm3 > 0) * ((Ttotwhl > 0) / p3eff + (wv > 9) * (Ttotwhl <= 0) * p3eff) *
            Ttotwhl / p3_gear) +
           (inp.U[1] != 1) * (inp.U[2] == 2) *
           ((wm3 > 0) * (Ttotwhl > 0) * Ttotwhl *
            ((inp.U[1] >= 0) * inp.U[1] / p3eff +
             (inp.U[1] < 0) * inp.U[1] * p3eff) / p3_gear))

    # 不可行输入检查
    inps = ((Te > 0) * (wg < 100) + (Ttotwhl < 0) * (Tm3 > 0) +
            (Ttotclu < 0) * (Tm1 > 0))

    # ========================================================================
    # 发动机
    # ========================================================================
    # 汽油低热值 = 42750000 J/kg

    # 发动机效率 (转速的函数) - 使用预创建的插值函数
    # 创建查询点
    query_points = np.column_stack([Te.flatten(), (wg * np.ones_like(Te)).flatten()])
    e_th = _eta_interp(query_points).reshape(Te.shape)

    # 燃油质量流量 (功率和效率的函数)
    m_dot_fuel = ((wg > 100) * ((Te >= 1) * Te * wg / e_th / 42750000 +
                                (Te < 1) * (Te > 0) * 0.14/1000))  # kg/s

    # 最大发动机扭矩 - 使用numpy.interp优化
    Te_max = np.interp(wg, _we0_list, _Tmax)

    # 燃油功率消耗
    Pe = m_dot_fuel * 42750000

    # 计算不可行
    ine = (Te > Te_max)

    # ========================================================================
    # 电机 (from ADVISOR PM25)
    # ========================================================================
    # P1和P3电机数据已移至全局预创建部分

    # P3电机数据已移至全局预创建部分

    # 电机效率计算 - 使用预创建的插值函数
    # 计算电机效率
    # 创建查询点 (扭矩, 转速)
    query_points_m1 = np.column_stack([Tm1.flatten(), (wm1 * np.ones_like(Tm1)).flatten()])
    query_points_m3 = np.column_stack([Tm3.flatten(), (wm3 * np.ones_like(Tm3)).flatten()])

    e1_raw = _etam1_interp(query_points_m1).reshape(Tm1.shape)
    e3_raw = _etam3_interp(query_points_m3).reshape(Tm3.shape)

    # (wm1~=0) .* interp_result + (wm1==0)
    e1 = np.where(wm1 != 0, e1_raw, 1.0)
    e3 = np.where(wm3 != 0, e3_raw, 1.0)

    # 汇总不可行 - 使用numpy.interp优化
    inm = (np.isnan(e1) +
           (Tm1 < 0) * (Tm1 < np.interp(wm1, _wm1_list, _Tm1min)) +
           (Tm1 >= 0) * (Tm1 > np.interp(wm1, _wm1_list, _Tm1max)) +
           np.isnan(e3) +
           (Tm3 < 0) * (Tm3 < np.interp(wm3, _wm3_list, _Tm3min)) +
           (Tm3 >= 0) * (Tm3 > np.interp(wm3, _wm3_list, _Tm3max)))

    # 处理NaN值
    e1 = np.where(np.isnan(e1), 1, e1)
    e3 = np.where(np.isnan(e3), 1, e3)

    # 计算电功率消耗
    Pm = ((Tm1 < 0) * wm1 * Tm1 * e1 +
          (Tm1 >= 0) * wm1 * Tm1 / e1 +
          (Tm3 < 0) * wm3 * Tm3 * e3 +
          (Tm3 >= 0) * wm3 * Tm3 / e3 + 1350)

    # ========================================================================
    # 电池
    # ========================================================================
    # 电池效率
    # 库仑效率 (充电时为0.98)
    e = np.where(Pm > 0, 1, 0.98)

    # 电池内阻 - 使用numpy.interp优化
    r = np.where(Pm > 0,
                 np.interp(inp.X[1], _soc_list, _R_dis),
                 np.interp(inp.X[1], _soc_list, _R_chg))

    # 电池电流限制
    # 电池容量 = 60 Ah
    # 最大放电电流 = 400A
    # 最大充电电流 = 400A
    im = np.where(Pm > 0, 400, 400)

    # 电池电压 - 使用numpy.interp优化
    v = np.interp(inp.X[1], _soc_list, _V_oc)

    # 电池电流
    Ib = e * (v - np.sqrt(v**2 - 4 * r * Pm)) / (2 * r)

    # 新的电池荷电状态
    X = {}
    X[1] = -Ib / (60 * 3600) + inp.X[1]

    # 电池功率消耗
    Pb = Ib * v

    # 更新不可行
    inb = ((v**2 < 4 * r * Pm) + (np.abs(Ib) > im))

    # 将新的荷电状态设置为实数值
    X[1] = np.real(X[1])
    Pb = np.real(Pb)
    Ib = np.real(Ib)

    # ========================================================================
    # 成本
    # ========================================================================
    # 汇总不可行矩阵
    I = (inps + inb + ine + inm != 0)

    # 计算成本矩阵 (燃油质量流量)
    # C{1}  = (Pe / 42750000);
    C = {}
    C[1] = m_dot_fuel

    # ========================================================================
    # 信号
    # ========================================================================
    # 在out中存储相关信号
    out['Tv'] = Tv
    out['Te'] = Te
    out['Tm1'] = Tm1
    out['Tm3'] = Tm3
    out['wg'] = wg
    out['wm1'] = wm1
    out['wm3'] = wm3
    out['Ib'] = Ib
    out['Pb'] = Pb
    out['m_dot_fuel'] = m_dot_fuel
    out['u1'] = inp.U[1]
    out['u2'] = inp.U[2]
    out['Ttotclu'] = Ttotclu
    out['e_th'] = e_th

    return X, C, I, out

