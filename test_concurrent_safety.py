#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 phev_ht21_serip1p3 函数的并发安全性
"""

import numpy as np
import time
import threading
import concurrent.futures
from multiprocessing import Pool, cpu_count
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input(seed=None):
    """创建测试输入数据"""
    if seed is not None:
        np.random.seed(seed)
    
    inp = Input()
    inp.X = {1: np.random.uniform(0.2, 0.8)}  # SOC 20%-80%
    inp.U = {1: np.random.uniform(0.3, 0.7), 2: np.random.randint(0, 3)}  # 随机扭矩分配和模式
    inp.W = {1: np.random.uniform(10.0, 25.0), 2: np.random.uniform(-0.5, 0.5)}  # 随机速度和加速度
    inp.Ts = 1.0
    return inp


def single_function_call(seed):
    """单次函数调用"""
    inp = create_test_input(seed)
    try:
        X, C, I, out = phev_ht21_serip1p3(inp)
        return {
            'seed': seed,
            'success': True,
            'soc_in': inp.X[1],
            'soc_out': X[1],
            'cost': C[1] if isinstance(C, dict) else C,
            'infeasible': I,
            'thread_id': threading.current_thread().ident
        }
    except Exception as e:
        return {
            'seed': seed,
            'success': False,
            'error': str(e),
            'thread_id': threading.current_thread().ident
        }


def test_thread_safety():
    """测试线程安全性"""
    print("1. 线程安全性测试")
    print("-" * 40)
    
    num_threads = 8
    calls_per_thread = 50
    total_calls = num_threads * calls_per_thread
    
    print(f"启动 {num_threads} 个线程，每个线程调用 {calls_per_thread} 次")
    print(f"总计 {total_calls} 次并发调用")
    
    start_time = time.perf_counter()
    
    # 使用ThreadPoolExecutor进行并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有任务
        futures = [executor.submit(single_function_call, i) for i in range(total_calls)]
        
        # 收集结果
        results = []
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    end_time = time.perf_counter()
    
    # 分析结果
    successful_calls = [r for r in results if r['success']]
    failed_calls = [r for r in results if not r['success']]
    
    print(f"\n结果统计:")
    print(f"  成功调用: {len(successful_calls)}/{total_calls}")
    print(f"  失败调用: {len(failed_calls)}")
    print(f"  总耗时: {end_time - start_time:.3f} 秒")
    print(f"  平均每次调用: {(end_time - start_time) / total_calls * 1000:.3f} ms")
    
    if failed_calls:
        print(f"\n失败详情:")
        for fail in failed_calls[:5]:  # 只显示前5个失败
            print(f"  种子 {fail['seed']}: {fail['error']}")
    
    # 检查结果一致性
    if len(successful_calls) > 1:
        print(f"\n一致性检查:")
        # 检查相同输入是否产生相同输出
        seed_groups = {}
        for result in successful_calls:
            seed = result['seed']
            if seed not in seed_groups:
                seed_groups[seed] = []
            seed_groups[seed].append(result)
        
        consistent = True
        for seed, group in seed_groups.items():
            if len(group) > 1:
                # 检查相同种子的结果是否一致
                first_result = group[0]
                for other_result in group[1:]:
                    if (abs(first_result['soc_out'] - other_result['soc_out']) > 1e-10 or
                        abs(first_result['cost'] - other_result['cost']) > 1e-10):
                        print(f"  ❌ 种子 {seed} 结果不一致")
                        consistent = False
                        break
        
        if consistent:
            print(f"  ✅ 所有相同输入产生一致输出")
        
        # 检查不同线程的结果
        thread_ids = set(r['thread_id'] for r in successful_calls)
        print(f"  使用了 {len(thread_ids)} 个不同线程")
    
    return len(successful_calls) == total_calls


def test_process_safety():
    """测试进程安全性"""
    print("\n2. 进程安全性测试")
    print("-" * 40)
    
    num_processes = min(4, cpu_count())
    calls_per_process = 25
    total_calls = num_processes * calls_per_process
    
    print(f"启动 {num_processes} 个进程，每个进程调用 {calls_per_process} 次")
    print(f"总计 {total_calls} 次并发调用")
    
    start_time = time.perf_counter()
    
    try:
        # 使用进程池进行并发测试
        with Pool(processes=num_processes) as pool:
            results = pool.map(single_function_call, range(total_calls))
        
        end_time = time.perf_counter()
        
        # 分析结果
        successful_calls = [r for r in results if r['success']]
        failed_calls = [r for r in results if not r['success']]
        
        print(f"\n结果统计:")
        print(f"  成功调用: {len(successful_calls)}/{total_calls}")
        print(f"  失败调用: {len(failed_calls)}")
        print(f"  总耗时: {end_time - start_time:.3f} 秒")
        print(f"  平均每次调用: {(end_time - start_time) / total_calls * 1000:.3f} ms")
        
        if failed_calls:
            print(f"\n失败详情:")
            for fail in failed_calls[:3]:
                print(f"  种子 {fail['seed']}: {fail['error']}")
        
        return len(successful_calls) == total_calls
        
    except Exception as e:
        print(f"❌ 进程池测试失败: {e}")
        return False


def test_concurrent_performance():
    """测试并发性能"""
    print("\n3. 并发性能测试")
    print("-" * 40)
    
    num_calls = 100
    
    # 串行执行
    print("串行执行测试...")
    start_time = time.perf_counter()
    serial_results = []
    for i in range(num_calls):
        result = single_function_call(i)
        serial_results.append(result)
    serial_time = time.perf_counter() - start_time
    
    # 并行执行 (线程)
    print("并行执行测试 (线程)...")
    start_time = time.perf_counter()
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(single_function_call, i) for i in range(num_calls)]
        parallel_results = [future.result() for future in concurrent.futures.as_completed(futures)]
    parallel_time = time.perf_counter() - start_time
    
    print(f"\n性能对比:")
    print(f"  串行执行: {serial_time:.3f} 秒 ({serial_time/num_calls*1000:.3f} ms/次)")
    print(f"  并行执行: {parallel_time:.3f} 秒 ({parallel_time/num_calls*1000:.3f} ms/次)")
    print(f"  加速比: {serial_time/parallel_time:.2f}x")
    
    # 检查结果一致性
    serial_success = len([r for r in serial_results if r['success']])
    parallel_success = len([r for r in parallel_results if r['success']])
    
    print(f"\n结果对比:")
    print(f"  串行成功率: {serial_success}/{num_calls}")
    print(f"  并行成功率: {parallel_success}/{num_calls}")
    
    return serial_success == parallel_success == num_calls


def analyze_thread_safety_factors():
    """分析线程安全性因素"""
    print("\n4. 线程安全性分析")
    print("-" * 40)
    
    print("分析函数的线程安全性因素:")
    
    print("\n✅ 线程安全的因素:")
    print("  - 函数是纯函数，没有修改全局状态")
    print("  - 所有计算都基于输入参数")
    print("  - 使用的NumPy操作是线程安全的")
    print("  - scipy.interpolate.RegularGridInterpolator是线程安全的")
    print("  - numpy.interp是线程安全的")
    print("  - 全局预创建的插值函数只读取，不修改")
    
    print("\n⚠️  需要注意的因素:")
    print("  - 全局变量是只读的，但在模块加载时创建")
    print("  - RegularGridInterpolator内部可能有缓存机制")
    print("  - NumPy数组操作在某些情况下可能不是完全线程安全的")
    
    print("\n📊 结论:")
    print("  当前函数设计支持并发调用，因为:")
    print("  1. 函数是无状态的")
    print("  2. 不修改任何全局变量")
    print("  3. 所有计算都是基于输入参数的局部操作")
    print("  4. 使用的库函数都是线程安全的")


def main():
    """主函数"""
    print("PHEV HT21 SerIP1P3 函数并发安全性测试")
    print("=" * 60)
    
    # 测试线程安全性
    thread_safe = test_thread_safety()
    
    # 测试进程安全性
    process_safe = test_process_safety()
    
    # 测试并发性能
    performance_ok = test_concurrent_performance()
    
    # 分析线程安全性因素
    analyze_thread_safety_factors()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  线程安全性: {'✅ 通过' if thread_safe else '❌ 失败'}")
    print(f"  进程安全性: {'✅ 通过' if process_safe else '❌ 失败'}")
    print(f"  并发性能: {'✅ 正常' if performance_ok else '❌ 异常'}")
    
    if thread_safe and process_safe:
        print("\n🎉 函数支持并发调用！")
        print("   可以安全地在多线程和多进程环境中使用")
    else:
        print("\n⚠️  函数可能不完全支持并发调用")
        print("   建议在并发环境中谨慎使用")


if __name__ == "__main__":
    main()
